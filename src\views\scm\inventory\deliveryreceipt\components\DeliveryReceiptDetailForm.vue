<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px" :border="true" show-summary :summary-method="summaryMethod">
      <!-- <el-table-column label="序号" type="index" width="100" /> -->
      <el-table-column label="序号" min-width="60" prop="num" align="center"/>
      <el-table-column label="物料信息" min-width="350">
        <template #default="{ row }">
          <div class="material-warehouse-source-cell">
            <!-- 物料信息 -->
            <div class="info-row">
              <span class="info-label">物料:</span>
              <span class="info-value">{{ formatMaterialDisplay(row) }}</span>
            </div>
            <!-- 仓库信息 -->
            <div class="info-row">
              <span class="info-label">仓库:</span>
              <span class="info-value">{{ getWarehouseName(row.warehouseId) }}</span>
            </div>
            <!-- 源单单号 -->
            <div class="info-row">
              <span class="info-label">源单:</span>
              <span class="info-value">{{ row.sourceNo || '-' }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- <el-table-column label="物料ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialId`" :rules="formRules.materialId" class="mb-0px!">
            <el-input v-model="row.materialId" placeholder="请输入物料ID" />
          </el-form-item>
        </template>
      </el-table-column> -->

      <!-- <el-table-column label="物料编号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialCode`" :rules="formRules.materialCode" class="mb-0px!">
            <el-input v-model="row.materialCode" placeholder="请输入物料编号" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="单位" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.unit`" :rules="formRules.unit" class="mb-0px!">
            <el-select
              v-model="row.unit"
              placeholder="请选择单位"
              clearable
              class="!w-130px"
              @change="(val) => handleUnitChange($index, val)"
              disabled
            >
              <el-option
                v-for="unit in unitList"
                :key="unit.id"
                :label="unit.name"
                :value="unit.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="应出库" min-width="100" prop="plannedQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.plannedQuantity`" :rules="formRules.plannedQuantity" class="mb-0px!">
            <el-input v-model="row.plannedQuantity" placeholder="请输入应收数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="实际出库" min-width="100" prop="fulfilledQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.fulfilledQuantity`" :rules="formRules.fulfilledQuantity" class="mb-0px!">
            <el-input v-model="row.fulfilledQuantity" placeholder="请输入实收数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="单价" min-width="100" prop="unitPrice">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.unitPrice`" :rules="formRules.unitPrice" class="mb-0px!">
            <el-input v-model="row.unitPrice" placeholder="请输入单价" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="金额" min-width="120" prop="amount">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.amount`" :rules="formRules.amount" class="mb-0px!">
            <el-input v-model="row.amount" placeholder="请输入金额" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" :rules="formRules.remark" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="基本单位应出库" min-width="130" prop="standardPlannedQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.standardPlannedQuantity`" :rules="formRules.standardPlannedQuantity" class="mb-0px!">
            <el-input v-model="row.standardPlannedQuantity" placeholder="请输入基本单位应收数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="基本单位实际出库" min-width="140" prop="standardFulfilledQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.standardFulfilledQuantity`" :rules="formRules.standardFulfilledQuantity" class="mb-0px!">
            <el-input v-model="row.standardFulfilledQuantity" placeholder="请输入基本单位实收数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="基本单位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.standardUnit`" :rules="formRules.standardUnit" class="mb-0px!">
            <el-select
              v-model="row.standardUnit"
              placeholder="请选择基本单位"
              clearable
              class="!w-130px"
              @change="(val) => handleStandardUnitChange($index, val)"
            >
              <el-option
                v-for="unit in unitList"
                :key="unit.id"
                :label="unit.name"
                :value="unit.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="含税单价" min-width="160" prop="taxPrice">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.taxPrice`" :rules="formRules.taxPrice" class="mb-0px!">
            <el-input v-model="row.taxPrice" placeholder="请输入含税单价" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="含税金额" min-width="160" prop="taxAmount">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.taxAmount`" :rules="formRules.taxAmount" class="mb-0px!">
            <el-input v-model="row.taxAmount" placeholder="请输入含税金额" />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="开票数量" min-width="150" prop="invoiceQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.invoiceQuantity`" :rules="formRules.invoiceQuantity" class="mb-0px!">
            <el-input v-model="row.invoiceQuantity" placeholder="请输入开票数量" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="开票金额" min-width="150" prop="invoiceAmount">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.invoiceAmount`" :rules="formRules.invoiceAmount" class="mb-0px!">
            <el-input v-model="row.invoiceAmount" placeholder="请输入开票金额" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="开票基本数量" min-width="150" prop="standardInvoiceQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.standardInvoiceQuantity`" :rules="formRules.standardInvoiceQuantity" class="mb-0px!">
            <el-input v-model="row.standardInvoiceQuantity" placeholder="请输入开票基本数量" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="库位" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.locationId`" :rules="formRules.locationId" class="mb-0px!">
            <el-select v-model="row.locationId" placeholder="请选择库位" :data="locationList" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="生产日期" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.effictiveDate`" :rules="formRules.effictiveDate" class="mb-0px!">
            <el-date-picker
              v-model="row.effictiveDate"
              type="date"
              value-format="x"
              placeholder="选择生产日期"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="失效日期" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.expiryDate`" :rules="formRules.expiryDate" class="mb-0px!">
            <el-date-picker
              v-model="row.expiryDate"
              type="date"
              value-format="x"
              placeholder="选择失效日期"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="说明" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.note`" :rules="formRules.note" class="mb-0px!">
            <el-input v-model="row.note" placeholder="请输入说明" />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="源单ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.sourceId`" :rules="formRules.sourceId" class="mb-0px!">
            <el-input v-model="row.sourceId" placeholder="请输入源单ID" />
          </el-form-item>
        </template>
      </el-table-column> -->

      <el-table-column label="批号" min-width="200">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.batchNo`" :rules="formRules.batchNo" class="mb-0px!">
            <el-select
              v-model="row.batchNo"
              placeholder="请选择批号"
              clearable
              filterable
              class="!w-180px"
              :disabled="!row.materialId"
            >
              <el-option
                v-for="batch in row.batchOptions || []"
                :key="batch.id"
                :label="batch.label"
                :value="batch.value"
                :disabled="batch.disabled"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="成本对象编码" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.costObjectId`" :rules="formRules.costObjectId" class="mb-0px!">
            <el-input v-model="row.costObjectId" placeholder="请输入成本对象编码" disabled/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="成本对象名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.costObjectName`" :rules="formRules.costObjectName" class="mb-0px!">
            <el-input v-model="row.costObjectName" placeholder="请输入成本对象名称" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="记账凭证号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.accountingVoucherNumber`" :rules="formRules.accountingVoucherNumber" class="mb-0px!">
            <el-input v-model="row.accountingVoucherNumber" placeholder="请输入记账凭证号" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <!-- <el-button @click="handleDelete($index)" link>—</el-button> -->
           <Icon icon="ep:delete" @click="handleDelete($index)" color="#f56c6c"/>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加销售出库明细</el-button>
  </el-row>
</template>
<script setup lang="ts">

import { DeliveryReceiptApi } from '@/api/scm/inventory/deliveryreceipt'
import { WarehouseLocationApi, WarehouseLocationVO } from '@/api/scm/inventory/warehouselocation'
import { WarehouseApi, WarehouseVO } from '@/api/scm/inventory/warehouse'
import { BatchInfoApi } from '@/api/scm/inventory/batchinfo'
import {
  mountBatchOptionsToRows,
  validateBatchData,
  cleanGeneratedBatchNumbers,
  type BatchDataByMaterial
} from '@/utils/batchUtils'
import { InventoryDetail } from '@/types/inventory'
import { getRemoteUnit } from '@/utils/commonBiz'
import { formatAmount, formatQuantity } from '@/utils/formatter'

const props = defineProps({
  bizOrderId: {
    type: [String, Number],
    default: undefined
  },
  warehouseId: {
    type: [String, Number],
    default: undefined
  }
})
const formLoading = ref(false) // 表单的加载中
const formData = ref<InventoryDetail[]>([])
const formRules = reactive<any>({
})
const formRef = ref() // 表单 Ref
const locationList = ref<WarehouseLocationVO[]>([])
const unitList = ref<any[]>([]) // 单位列表数据
const warehouseList = ref<WarehouseVO[]>([]) // 仓库列表数据
/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.bizOrderId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return;
    }
    try {
      formLoading.value = true
      const details = await DeliveryReceiptApi.getDeliveryReceiptDetailListByBizOrderId(val)
      
      // 为每行添加批次选项字段
      details.forEach(row => {
        row.batchOptions = []
      })

      // 批量加载所有物料的批次信息
      await loadBatchOptionsForMultipleMaterials(details)
      
      formData.value = details
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)
watch(
  () => props.warehouseId,
  async (val) => {
    formData.value = formData.value.map((item:InventoryDetail) => {
      item.warehouseId = val
      return item
    })
  }
)
/** 格式化物料显示信息 */
const formatMaterialDisplay = (row: any) => {
  if (!row) return '-'
  const code = row.materialCode || ''
  const name = row.materialName || ''

  // 构建显示标签，只有非空值才参与拼接
  const parts: string[] = []
  if (code) parts.push(code)
  if (name) parts.push(name)

  return parts.length > 0 ? parts.join(' - ') : '-'
}

/** 获取仓库名称 */
const getWarehouseName = (warehouseId: number | string) => {
  if (!warehouseId) return '-'
  const warehouse = warehouseList.value.find(w => w.id == warehouseId)
  return warehouse ? warehouse.name : '-'
}

/** 获取单位列表 */
const loadUnits = async () => {
  try {
    unitList.value = await getRemoteUnit()
  } catch (error) {
    console.error('获取单位数据失败:', error)
    unitList.value = []
  }
}

/** 批量加载批次选项（多个物料） */
const loadBatchOptionsForMultipleMaterials = async (rows: any[]) => {
  // 收集所有需要加载批次的物料ID
  const materialIds = rows
    .filter(row => row.materialId)
    .map(row => Number(row.materialId))
    .filter((id, index, arr) => arr.indexOf(id) === index) // 去重

  if (materialIds.length === 0) {
    // 如果没有物料ID，清空所有行的批次选项
    mountBatchOptionsToRows(rows, {}, false)
    return
  }

  try {
    // 使用批量接口获取批次数据
    const response = await BatchInfoApi.getSimpleBatchInfoListByMaterialIds({
      materialIds: materialIds
    })

    // 验证批次数据
    const validation = validateBatchData(response as BatchDataByMaterial)
    if (!validation.isValid) {
      validation.errors.forEach(error => console.error(error))
    }
    if (validation.warnings.length > 0) {
      validation.warnings.forEach(warning => console.warn(warning))
    }

    // 使用工具类挂载批次选项到行数据
    mountBatchOptionsToRows(rows, response as BatchDataByMaterial, true)
  } catch (error) {
    console.error('批量加载批次信息失败:', error)
    // 出错时使用工具类清空所有行的批次选项
    mountBatchOptionsToRows(rows, {}, false)
  }
}

/** 加载批次选项（单个物料，向后兼容） */
const loadBatchOptions = async (row: any, materialId: string | number) => {
  if (!materialId) {
    row.batchOptions = []
    row.batchNo = undefined
    return
  }

  // 使用批量接口处理单个物料
  await loadBatchOptionsForMultipleMaterials([row])
}

// formatBatchLabel 函数已移至 @/utils/batchUtils 工具类中

//初始化方法
onMounted(async () => {
  await initLocationList()
  await loadUnits()
  await loadWarehouseList()
  
  // 如果初始化时已有物料数据，加载对应的批次数据
  if (formData.value && formData.value.length > 0) {
    // 为每行添加批次选项字段
    formData.value.forEach(row => {
      if (!row.batchOptions) {
        row.batchOptions = []
      }
    })

    // 批量加载所有物料的批次信息
    await loadBatchOptionsForMultipleMaterials(formData.value)
  }
})

/** 新增按钮操作 */
const handleAdd = () => {
  const row:InventoryDetail = {
    id: undefined,
    num: undefined,
    bizOrderId: undefined,
    bizOrderNo: undefined,
    warehouseId: undefined,
    locationId: undefined,
    materialId: undefined,
    materialName: undefined,
    materialCode: undefined,
    unit: undefined,
    unitPrice: undefined,
    amount: undefined,
    remark: undefined,
    plannedQuantity: undefined,
    fulfilledQuantity: undefined,
    standardPlannedQuantity: undefined,
    standardFulfilledQuantity: undefined,
    standardUnit: undefined,
    taxPrice: undefined,
    taxAmount: undefined,
    invoiceQuantity: undefined,
    invoiceAmount: undefined,
    standardInvoiceQuantity: undefined,
    effictiveDate: undefined,
    expiryDate: undefined,
    note: undefined,
    sourceId: undefined,
    sourceNo: undefined,
    sourceType: undefined,
    sourceDetailId: undefined,
    batchNo: undefined,
    batchOptions: [], // 添加批次选项
    costObjectId: undefined,
    costObjectName: undefined,
    accountingVoucherNumber: undefined,
  }
  row.bizOrderId = props.bizOrderId
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

//库位信息相关方法
const initLocationList = async () => {
  const res = await WarehouseLocationApi.getWarehouseLocationPage({
    pageNo:1,
    pageSize:10
  })
  locationList.value = res
}



// 处理单位选择变化
const handleUnitChange = (index: number, unitName: string) => {
  if (!unitName) {
    formData.value[index].unit = undefined
    formData.value[index].unitName = undefined
    return
  }

  // 根据单位名称找到对应的单位ID
  const selectedUnit = unitList.value.find(unit => unit.name === unitName)
  if (selectedUnit) {
    formData.value[index].unit = selectedUnit.id
    formData.value[index].unitName = selectedUnit.name
  }
}

// 处理基本单位选择变化
const handleStandardUnitChange = (index: number, unitName: string) => {
  if (!unitName) {
    formData.value[index].standardUnit = undefined
    formData.value[index].standardUnitName = undefined
    return
  }

  // 根据单位名称找到对应的单位ID
  const selectedUnit = unitList.value.find(unit => unit.name === unitName)
  if (selectedUnit) {
    formData.value[index].standardUnit = selectedUnit.id
    formData.value[index].standardUnitName = selectedUnit.name
  }
}

/** 加载仓库数据 */
const loadWarehouseList = async () => {
  try {
    const data = await WarehouseApi.getWarehouseList({})
    warehouseList.value = data || []
  } catch (error) {
    console.error('加载仓库数据失败:', error)
    warehouseList.value = []
  }
}



/** 表单值 */
const getData = () => {
  // 过滤掉batchOptions字段，避免传递给后端
  const cleanData = formData.value.map(item => {
    const { batchOptions, ...rest } = item
    return rest
  })

  // 清理生成的批号
  return cleanGeneratedBatchNumbers(cleanData)
}

/** 设置表单数据 */
const setData = async (data: InventoryDetail[]) => {
  if (!data || data.length === 0) {
    formData.value = []
    return
  }

  // 为每行添加批次选项字段
  data.forEach(row => {
    row.batchOptions = []
  })

  // 批量加载所有物料的批次信息
  await loadBatchOptionsForMultipleMaterials(data)

  formData.value = data
}

/** 表格汇总方法 */
const summaryMethod = ({ columns, data }: { columns: any[], data: any[] }) => {
  const sums: any[] = []

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const quantityFields = ['plannedQuantity', 'fulfilledQuantity', 'standardPlannedQuantity',
                           'standardFulfilledQuantity', 'invoiceQuantity', 'standardInvoiceQuantity']

    // 需要汇总的金额字段
    const amountFields = ['unitPrice', 'amount', 'taxPrice', 'taxAmount', 'invoiceAmount']

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map(item => Number(item[column.property]) || 0)
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatQuantity(total)
    } else if (amountFields.includes(column.property)) {
      // 金额字段汇总
      const values = data.map(item => Number(item[column.property]) || 0)
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatAmount(total)
    } else {
      // 其他字段不汇总
      sums[index] = ''
    }
  })

  return sums
}

defineExpose({ validate, getData, setData })
</script>

<style scoped>
.material-warehouse-source-cell {
  padding: 4px 0;
  line-height: 1.4;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
  font-size: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #606266;
  font-weight: 500;
  min-width: 50px;
  flex-shrink: 0;
}

.info-value {
  color: #303133;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
