<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" :width="1200">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="供应商名称" prop="supplierName">
            <ScrollSelect
              v-model="formData.supplierId"
              :loadMethod="loadSuppliers"
              :labelKey="'name'"
              :valueKey="'id'"
              :queryKey="'name'"
              :defaultValue="formData.supplierDefaultValue"
              placeholder="请选择供应商"
              style="width: 100%"
              @change="handleSupplierChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="订单编号" prop="orderNo">
            <el-input v-model="formData.orderNo" placeholder="请输入订单编号(自动生成)" disabled/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="订单日期" prop="orderDate">
            <el-date-picker
              v-model="formData.orderDate"
              type="date"
              value-format="x"
              placeholder="选择订单日期"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="交货日期" prop="deliveryDate">
            <el-date-picker
              v-model="formData.deliveryDate"
              type="date"
              value-format="x"
              placeholder="选择交货日期"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="订单状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择订单状态" style="width: 100%" disabled>
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.PURCHASE_ORDER_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="订单总金额" prop="totalAmount">
            <el-input v-model="formData.totalAmount" placeholder="请输入订单总金额" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
<!--        <el-col :span="12">-->
<!--          <el-form-item label="入库状态" prop="isInStock">-->
<!--            <el-select v-model="formData.isInStock" placeholder="请选择入库状态" style="width: 100%">-->
<!--              <el-option-->
<!--                v-for="dict in getStrDictOptions(DICT_TYPE.COMMON_YES_NO)"-->
<!--                :key="dict.value"-->
<!--                :label="dict.label"-->
<!--                :value="dict.value"-->
<!--              />-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--        </el-col>-->
        <el-col :span="12">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="审批单号" prop="approveNo">
            <el-input v-model="formData.approveNo" placeholder="请输入审批单号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="审批人ID" prop="approver">
            <el-input v-model="formData.approver" placeholder="请输入审批人ID" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="审批时间" prop="approvalTime">
            <el-date-picker
              v-model="formData.approvalTime"
              type="date"
              value-format="x"
              placeholder="选择审批时间"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="审批状态" prop="approvalStatus">
            <el-radio-group v-model="formData.approvalStatus">
              <el-radio
                v-for="dict in getStrDictOptions(DICT_TYPE.APPROVE_STATUS)"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="采购订单产品" name="orderDetail">
        <OrderDetailForm
          ref="orderDetailFormRef"
          :order-id="formData.id"
          :supplier-id="formData.supplierId"
          :supplier-name="formData.supplierName"
          :order-date="formData.orderDate"
          :order-no="formData.orderNo"
          @total-amount-change="handleTotalAmountChange"
        />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { OrderApi, OrderVO } from '@/api/scm/purchase/order'
import { CompanyApi } from '@/api/scm/base/company'
import ScrollSelect from '@/components/ScrollSelect/index.vue'
import OrderDetailForm from './components/OrderDetailForm.vue'

/** 采购订单 表单 */
defineOptions({ name: 'OrderForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  supplierId: undefined,
  supplierName: undefined,
  supplierDefaultValue: undefined as any,
  orderNo: undefined,
  orderDate: undefined,
  deliveryDate: undefined,
  status: undefined,
  totalAmount: undefined as number | undefined,
  remark: undefined,
  isInStock: undefined,
  approveNo: undefined,
  approver: undefined,
  approvalTime: undefined,
  approvalStatus: undefined,
})
const formRules = reactive({
  supplierName: [{ required: true, message: '供应商名称不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 子表的表单 */
const subTabsName = ref('orderDetail')
const orderDetailFormRef = ref()

/** 打开弹窗 */
const open = async (type: string, id?: number, requirementData?: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await OrderApi.getOrder(id)
      // 转换布尔值为字符串，以匹配字典选项
      if (typeof data.isInStock === 'boolean') {
        data.isInStock = data.isInStock ? '1' : '0'
      }
      // 设置供应商默认值
      if (data.supplierId && data.supplierName) {
        data.supplierDefaultValue = {
          id: data.supplierId,
          name: data.supplierName
        }
      }
      formData.value = data
    } finally {
      formLoading.value = false
    }
  } else if (requirementData) {
    // 从采购需求创建订单时，预填充数据
    const currentDate = new Date().getTime()

    // 判断是单个需求还是多个需求
    const isMultipleRequirements = Array.isArray(requirementData)
    const firstRequirement = isMultipleRequirements ? requirementData[0] : requirementData

    ;(formData.value as any).orderDate = currentDate
    ;(formData.value as any).deliveryDate = firstRequirement.expectedDeliveryDate ? new Date(firstRequirement.expectedDeliveryDate).getTime() : currentDate
    ;(formData.value as any).status = '0' // 草稿状态
    ;(formData.value as any).isInStock = '0' // 入库状态：未入库
    ;(formData.value as any).remark = firstRequirement.remark || ''

    // 通知子组件需求数据已准备好
    nextTick(async () => {
      if (orderDetailFormRef.value) {
        if (isMultipleRequirements) {
          // 处理多个需求
          await orderDetailFormRef.value.setMultipleRequirementData(requirementData)
        } else {
          // 处理单个需求
          await orderDetailFormRef.value.setRequirementData(requirementData)
        }
      }
    })
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await orderDetailFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'orderDetail'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data:any = { ...formData.value } as unknown as OrderVO
    // 转换字符串为布尔值，以匹配后端期望的数据类型
    if (typeof data.isInStock === 'string') {
      data.isInStock = data.isInStock === '1'
    }
    // 移除不需要传给后端的字段
    delete (data as any).supplierDefaultValue

    // 拼接子表的数据
    const orderDetails = orderDetailFormRef.value.getData()
    
    // 清理子表数据中不需要传给后端的字段
    data.orderDetails = orderDetails.map((detail: any) => {
      const cleanDetail = { ...detail }
      // 删除前端显示用的默认值字段
      delete cleanDetail.supplierDefaultValue
      delete cleanDetail.materialDefaultValue
      delete cleanDetail.unitDefaultValue
      delete cleanDetail.auxilaryUnitDefaultValue

      // 确保sourceId字段正确映射
      if (cleanDetail.requirementId) {
        cleanDetail.sourceId = cleanDetail.requirementId
        // 保留requirementId字段，因为可能后端也需要这个字段
      }

      // 确保规格字段正确映射 - 将spec字段保留，后端应该接受这个字段
      // 不修改remark字段，只保留spec字段

      // 确保records字段被正确处理
      if (cleanDetail.records && Array.isArray(cleanDetail.records)) {
        // 保留records字段，这里存储了多个需求单的信息
        // 确保每个record中的必要字段都存在
        cleanDetail.records = cleanDetail.records.map((record: any) => {
          // 确保orderId字段与当前订单ID一致
          if (formType.value === 'create') {
            // 新建时，orderId会在后端生成，这里不需要设置
            delete record.orderId
            delete record.orderNo
          } else if (data.id) {
            // 编辑时，设置为当前订单ID
            record.orderId = data.id
            record.orderNo = data.orderNo
          }
          return record
        })
      } else {
        // 如果没有records字段，创建一个包含当前需求信息的记录
        cleanDetail.records = [{
          id: undefined,
          requirementId: cleanDetail.requirementId,
          requirementNo: cleanDetail.sourceNo,
          quantity: cleanDetail.quantity,
          remark: cleanDetail.remark || ''
        }]
      }

      return cleanDetail
    })

    // 调试：打印提交到后端的数据结构
    console.log('提交到后端的采购订单数据:', {
      orderDetails: data.orderDetails.map(detail => ({
        materialName: detail.materialName,
        spec: detail.spec
      }))
    })
    if (formType.value === 'create') {
      await OrderApi.createOrder(data)
      message.success(t('common.createSuccess'))
    } else {
      await OrderApi.updateOrder(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 加载供应商数据 */
const loadSuppliers = async (params) => {
  try {
    const response = await CompanyApi.getSimpleCompanyPage({
      pageNo: params.pageNo || 1,
      pageSize: params.pageSize || 20,
      name: params.name || params.query || '',
      isSupplier: true // 只获取供应商
    })
    return {
      list: response.list || [],
      total: response.total || 0
    }
  } catch (error) {
    return { list: [], total: 0 }
  }
}

/** 供应商变更处理 */
const handleSupplierChange = (selectedValue, selectedItem) => {
  if (selectedItem) {
    formData.value.supplierId = selectedItem.id
    formData.value.supplierName = selectedItem.name || selectedItem.shortName
    // 设置默认值，用于下次显示
    formData.value.supplierDefaultValue = {
      id: selectedItem.id,
      name: selectedItem.name || selectedItem.shortName
    }
  } else {
    formData.value.supplierId = undefined
    formData.value.supplierName = undefined
    formData.value.supplierDefaultValue = undefined
  }
}

/** 处理子表总金额变化 */
const handleTotalAmountChange = (totalAmount: number) => {
  formData.value.totalAmount = totalAmount
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    supplierId: undefined,
    supplierName: undefined,
    supplierDefaultValue: undefined,
    orderNo: undefined,
    orderDate: undefined,
    deliveryDate: undefined,
    status: undefined,
    totalAmount: undefined,
    remark: undefined,
    isInStock: undefined,
    approveNo: undefined,
    approver: undefined,
    approvalTime: undefined,
    approvalStatus: undefined,
  }
  formRef.value?.resetFields()
}
</script>
