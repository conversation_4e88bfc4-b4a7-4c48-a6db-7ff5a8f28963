/**
 * 批次信息处理工具类
 * 用于处理批次数据的格式化、验证和挂载
 */

// 批次信息接口定义
export interface BatchInfo {
  id: number
  batchNo: string | null
  quantity: number
  lockQuantity: number
  unlockQuantity: number
  materialId?: number
  materialName?: string
  materialCode?: string
  effectiveDate?: string
  expiryDate?: string
}

// 批次选项接口（用于选择器显示）
export interface BatchOption {
  id: number
  batchNo: string
  label: string
  value: string
  quantity: number
  lockQuantity: number
  unlockQuantity: number
  disabled?: boolean
  materialId?: number
}

// 按物料ID分组的批次数据类型
export type BatchDataByMaterial = Record<string | number, BatchInfo[]>

/**
 * 格式化批次显示标签
 * @param batch 批次信息
 * @returns 格式化后的显示标签
 */
export function formatBatchLabel(batch: BatchInfo): string {
  if (!batch) return ''
  
  const batchNo = batch.batchNo || '无批号'
  const quantity = batch.quantity || 0
  const unlockQuantity = batch.unlockQuantity || 0
  
  return `${batchNo} (库存:${quantity}, 可用:${unlockQuantity})`
}

/**
 * 将批次信息转换为选择器选项
 * @param batch 批次信息
 * @returns 选择器选项
 */
export function batchToOption(batch: BatchInfo): BatchOption {
  const batchNo = batch.batchNo || `无批号`
  const label = formatBatchLabel(batch)

  // 使用批次ID作为唯一值，避免多个null批号导致的选择冲突
  const uniqueValue = batch.batchNo || `batch_${batch.id}`

  return {
    id: batch.id,
    batchNo: batchNo,
    label: label,
    value: uniqueValue, // 使用唯一值
    quantity: batch.quantity,
    lockQuantity: batch.lockQuantity,
    unlockQuantity: batch.unlockQuantity,
    disabled: batch.unlockQuantity <= 0, // 可用数量为0时禁用
    materialId: batch.materialId
  }
}

/**
 * 批量转换批次信息为选择器选项
 * @param batches 批次信息数组
 * @returns 选择器选项数组
 */
export function batchesToOptions(batches: BatchInfo[]): BatchOption[] {
  if (!Array.isArray(batches)) {
    return []
  }
  
  return batches
    .filter(batch => batch && typeof batch === 'object')
    .map(batch => batchToOption(batch))
    .sort((a, b) => {
      // 按可用数量降序排列，可用数量大的排在前面
      if (a.unlockQuantity !== b.unlockQuantity) {
        return b.unlockQuantity - a.unlockQuantity
      }
      // 可用数量相同时，按批次号排序
      return a.batchNo.localeCompare(b.batchNo)
    })
}

/**
 * 处理按物料ID分组的批次数据，转换为选择器选项
 * @param batchDataByMaterial 按物料ID分组的批次数据
 * @returns 按物料ID分组的选择器选项
 */
export function processBatchDataByMaterial(
  batchDataByMaterial: BatchDataByMaterial
): Record<string | number, BatchOption[]> {
  const result: Record<string | number, BatchOption[]> = {}
  
  for (const [materialId, batches] of Object.entries(batchDataByMaterial)) {
    result[materialId] = batchesToOptions(batches)
  }
  
  return result
}

/**
 * 为表单行数据挂载批次选项
 * @param rows 表单行数据数组
 * @param batchDataByMaterial 按物料ID分组的批次数据
 * @param autoSelectFirst 是否自动选择第一个可用批次
 */
export function mountBatchOptionsToRows(
  rows: any[],
  batchDataByMaterial: BatchDataByMaterial,
  autoSelectFirst: boolean = true
): void {
  if (!Array.isArray(rows)) {
    return
  }

  const batchOptionsByMaterial = processBatchDataByMaterial(batchDataByMaterial)

  rows.forEach(row => {
    if (!row || typeof row !== 'object') {
      return
    }
    
    if (row.materialId) {
      const materialId = Number(row.materialId)
      const batchOptions = batchOptionsByMaterial[materialId] || []
      
      // 挂载批次选项
      row.batchOptions = batchOptions
      
      // 自动选择第一个可用批次
      if (autoSelectFirst && batchOptions.length > 0) {
        const firstAvailableBatch = batchOptions.find(option => !option.disabled)
        if (firstAvailableBatch) {
          row.batchNo = firstAvailableBatch.value // 使用唯一值而不是显示名称
          row.selectedBatch = firstAvailableBatch
        } else {
          // 如果没有可用批次，选择第一个但标记为不可用
          row.batchNo = batchOptions[0].value // 使用唯一值而不是显示名称
          row.selectedBatch = batchOptions[0]
        }
      } else if (!autoSelectFirst || batchOptions.length === 0) {
        row.batchNo = undefined
        row.selectedBatch = undefined
      }
    } else {
      // 没有物料ID时清空批次选项
      row.batchOptions = []
      row.batchNo = undefined
      row.selectedBatch = undefined
    }
  })
}

/**
 * 清理表单数据中的生成批号
 * 将形如 "batch_xx" 的生成批号设置为 undefined，避免污染后端数据
 * @param formData 表单数据数组
 * @returns 清理后的表单数据
 */
export function cleanGeneratedBatchNumbers(formData: any[]): any[] {
  return formData.map(item => {
    // 如果批号是生成的batch_xx格式，则设置为undefined
    if (item.batchNo && typeof item.batchNo === 'string' && item.batchNo.startsWith('batch_')) {
      return { ...item, batchNo: undefined }
    }
    return item
  })
}

/**
 * 验证批次数据的完整性
 * @param batchDataByMaterial 按物料ID分组的批次数据
 * @returns 验证结果
 */
export function validateBatchData(batchDataByMaterial: BatchDataByMaterial): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []

  if (!batchDataByMaterial || typeof batchDataByMaterial !== 'object') {
    errors.push('批次数据不是有效的对象')
    return { isValid: false, errors, warnings }
  }

  for (const [materialId, batches] of Object.entries(batchDataByMaterial)) {
    if (!Array.isArray(batches)) {
      errors.push(`物料ID ${materialId} 的批次数据不是数组`)
      continue
    }
    
    batches.forEach((batch, index) => {
      if (!batch || typeof batch !== 'object') {
        errors.push(`物料ID ${materialId} 的第 ${index + 1} 个批次数据无效`)
        return
      }
      
      // 检查必需字段
      if (typeof batch.id !== 'number') {
        errors.push(`物料ID ${materialId} 的批次缺少有效的ID`)
      }
      
      if (typeof batch.quantity !== 'number' || batch.quantity < 0) {
        warnings.push(`物料ID ${materialId} 的批次 ${batch.id} 库存数量异常: ${batch.quantity}`)
      }
      
      if (typeof batch.unlockQuantity !== 'number' || batch.unlockQuantity < 0) {
        warnings.push(`物料ID ${materialId} 的批次 ${batch.id} 可用数量异常: ${batch.unlockQuantity}`)
      }
      
      // 检查数量逻辑
      if (batch.unlockQuantity > batch.quantity) {
        warnings.push(`物料ID ${materialId} 的批次 ${batch.id} 可用数量(${batch.unlockQuantity})大于库存数量(${batch.quantity})`)
      }
    })
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 获取物料的可用批次数量统计
 * @param materialId 物料ID
 * @param batchDataByMaterial 按物料ID分组的批次数据
 * @returns 统计信息
 */
export function getMaterialBatchStats(
  materialId: string | number,
  batchDataByMaterial: BatchDataByMaterial
): {
  totalBatches: number
  availableBatches: number
  totalQuantity: number
  totalUnlockQuantity: number
} {
  const batches = batchDataByMaterial[materialId] || []
  
  return {
    totalBatches: batches.length,
    availableBatches: batches.filter(batch => batch.unlockQuantity > 0).length,
    totalQuantity: batches.reduce((sum, batch) => sum + (batch.quantity || 0), 0),
    totalUnlockQuantity: batches.reduce((sum, batch) => sum + (batch.unlockQuantity || 0), 0)
  }
}


